<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth Diagnostic - Forge EC</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e293b, #0f172a);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .success { background: rgba(16, 185, 129, 0.2); border-left: 4px solid #10b981; }
        .error { background: rgba(239, 68, 68, 0.2); border-left: 4px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border-left: 4px solid #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.2); border-left: 4px solid #3b82f6; }
        
        .test-button {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }
        
        .test-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 5px;
        }
        
        .log-success { background: rgba(16, 185, 129, 0.1); }
        .log-error { background: rgba(239, 68, 68, 0.1); }
        .log-warning { background: rgba(245, 158, 11, 0.1); }
        .log-info { background: rgba(59, 130, 246, 0.1); }
        
        .config-display {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Authentication Diagnostic</h1>
        <p>This tool diagnoses Firebase Authentication configuration issues for the Forge EC project.</p>
        
        <div id="status-container">
            <div class="status info">
                <strong>Status:</strong> Initializing diagnostic...
            </div>
        </div>
        
        <div class="test-controls">
            <button class="test-button" id="test-config">Test Configuration</button>
            <button class="test-button" id="test-auth-methods">Test Auth Methods</button>
            <button class="test-button" id="test-email-signup">Test Email Signup</button>
            <button class="test-button" id="test-email-signin">Test Email Signin</button>
            <button class="test-button" id="clear-logs">Clear Logs</button>
        </div>
        
        <div class="config-display" id="config-display">
            Loading Firebase configuration...
        </div>
        
        <div class="log-container" id="log-container">
            <div class="log-entry log-info">Diagnostic tool started...</div>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module">
        // Firebase CDN imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, GoogleAuthProvider, GithubAuthProvider, signInWithPopup } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
            authDomain: "forge-ec.firebaseapp.com",
            databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
            projectId: "forge-ec",
            storageBucket: "forge-ec.firebasestorage.app",
            messagingSenderId: "436060720516",
            appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
            measurementId: "G-1BVB7FLGRJ"
        };

        let app, auth;
        
        // Logging function
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}"><strong>Status:</strong> ${message}</div>`;
        }

        // Initialize Firebase
        try {
            app = initializeApp(firebaseConfig);
            auth = getAuth(app);
            
            // Display configuration
            document.getElementById('config-display').textContent = JSON.stringify(firebaseConfig, null, 2);
            
            addLog('Firebase app initialized successfully', 'success');
            addLog(`Project ID: ${firebaseConfig.projectId}`, 'info');
            addLog(`Auth Domain: ${firebaseConfig.authDomain}`, 'info');
            
            updateStatus('Firebase initialized successfully', 'success');
            
            // Enable test buttons
            document.querySelectorAll('.test-button').forEach(btn => btn.disabled = false);
            
        } catch (error) {
            addLog(`Firebase initialization failed: ${error.message}`, 'error');
            updateStatus('Firebase initialization failed', 'error');
        }

        // Test functions
        window.testConfiguration = async function() {
            addLog('Testing Firebase configuration...', 'info');
            
            try {
                // Test if auth is properly configured
                if (!auth) {
                    throw new Error('Firebase Auth not initialized');
                }
                
                addLog('✅ Firebase Auth instance exists', 'success');
                addLog(`Auth domain: ${auth.config.authDomain}`, 'info');
                addLog(`Project ID: ${auth.config.projectId}`, 'info');
                
                // Test auth state
                auth.onAuthStateChanged((user) => {
                    if (user) {
                        addLog(`Current user: ${user.email}`, 'info');
                    } else {
                        addLog('No user currently signed in', 'info');
                    }
                });
                
                updateStatus('Configuration test completed', 'success');
                
            } catch (error) {
                addLog(`Configuration test failed: ${error.message}`, 'error');
                updateStatus('Configuration test failed', 'error');
            }
        };

        window.testAuthMethods = async function() {
            addLog('Testing available authentication methods...', 'info');
            
            try {
                // Test Google provider
                const googleProvider = new GoogleAuthProvider();
                addLog('✅ Google Auth provider created', 'success');
                
                // Test GitHub provider
                const githubProvider = new GithubAuthProvider();
                addLog('✅ GitHub Auth provider created', 'success');
                
                addLog('✅ Email/Password auth available', 'success');
                
                updateStatus('Auth methods test completed', 'success');
                
            } catch (error) {
                addLog(`Auth methods test failed: ${error.message}`, 'error');
                updateStatus('Auth methods test failed', 'error');
            }
        };

        window.testEmailSignup = async function() {
            const testEmail = `test-${Date.now()}@example.com`;
            const testPassword = 'TestPassword123!';
            
            addLog(`Testing email signup with: ${testEmail}`, 'info');
            
            try {
                const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
                addLog(`✅ Email signup successful: ${userCredential.user.email}`, 'success');
                
                // Clean up - delete the test user
                await userCredential.user.delete();
                addLog('Test user deleted', 'info');
                
                updateStatus('Email signup test completed successfully', 'success');
                
            } catch (error) {
                addLog(`❌ Email signup failed: ${error.code} - ${error.message}`, 'error');
                updateStatus(`Email signup test failed: ${error.code}`, 'error');
                
                // Log detailed error information
                if (error.code === 'auth/configuration-not-found') {
                    addLog('🔍 This error indicates Firebase Auth is not properly configured in the Firebase Console', 'warning');
                    addLog('🔧 Please check: Authentication > Sign-in method in Firebase Console', 'warning');
                }
            }
        };

        window.testEmailSignin = async function() {
            addLog('Testing email signin with non-existent user...', 'info');
            
            try {
                await signInWithEmailAndPassword(auth, '<EMAIL>', 'wrongpassword');
                addLog('Unexpected success with invalid credentials', 'warning');
                
            } catch (error) {
                if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential') {
                    addLog(`✅ Email signin properly configured (expected error: ${error.code})`, 'success');
                    updateStatus('Email signin test completed successfully', 'success');
                } else {
                    addLog(`❌ Email signin failed: ${error.code} - ${error.message}`, 'error');
                    updateStatus(`Email signin test failed: ${error.code}`, 'error');
                    
                    if (error.code === 'auth/configuration-not-found') {
                        addLog('🔍 This error indicates Firebase Auth is not properly configured', 'warning');
                    }
                }
            }
        };

        window.clearLogs = function() {
            document.getElementById('log-container').innerHTML = '<div class="log-entry log-info">Logs cleared...</div>';
        };

        // Event listeners
        document.getElementById('test-config').addEventListener('click', testConfiguration);
        document.getElementById('test-auth-methods').addEventListener('click', testAuthMethods);
        document.getElementById('test-email-signup').addEventListener('click', testEmailSignup);
        document.getElementById('test-email-signin').addEventListener('click', testEmailSignin);
        document.getElementById('clear-logs').addEventListener('click', clearLogs);

    </script>
</body>
</html>
