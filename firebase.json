{"hosting": {"public": ".", "site": "forge-ec", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/*.md", "functions/**", "firestore.rules", "storage.rules"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(html|json)", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "functions", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}