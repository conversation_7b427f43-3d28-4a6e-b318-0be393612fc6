rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.email_verified == true;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.admin == true;
    }
    
    // Documentation collection - public read, admin write
    match /documentation/{docId} {
      allow read: if true; // Public read access
      allow write: if isAdmin(); // Only admins can modify docs
      
      // Allow tracking document views
      allow update: if isAuthenticated() && 
                   onlyUpdating(['views', 'lastViewed', 'updatedAt']);
    }
    
    // User profiles - users can read/write their own
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin(); // <PERSON><PERSON> can read all profiles
      
      // Allow creation during signup
      allow create: if isAuthenticated() && 
                   request.auth.uid == userId &&
                   validateUserData();
    }
    
    // Comments - authenticated users can create, owners can modify
    match /comments/{commentId} {
      allow read: if true; // Public read
      allow create: if isValidUser() && 
                   validateCommentData() &&
                   request.auth.uid == resource.data.userId;
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }
    
    // Bookmarks - users can manage their own
    match /bookmarks/{bookmarkId} {
      allow read, write: if isAuthenticated() && 
                        request.auth.uid == resource.data.userId;
      allow create: if isAuthenticated() && 
                   request.auth.uid == request.resource.data.userId &&
                   validateBookmarkData();
    }
    
    // Feedback - authenticated users can create, admins can read all
    match /feedback/{feedbackId} {
      allow create: if isValidUser() && 
                   validateFeedbackData() &&
                   request.auth.uid == request.resource.data.userId;
      allow read: if isAdmin() || 
                 (isAuthenticated() && request.auth.uid == resource.data.userId);
      allow update: if isAdmin(); // Only admins can update feedback status
    }
    
    // Analytics - write for authenticated users, read for admins
    match /doc_analytics/{analyticsId} {
      allow create: if isAuthenticated() && 
                   validateAnalyticsData();
      allow read: if isAdmin();
    }
    
    // Search analytics - similar to doc analytics
    match /search_analytics/{searchId} {
      allow create: if isAuthenticated();
      allow read: if isAdmin();
    }
    
    // Contact form submissions - create only, admin read
    match /contact_submissions/{submissionId} {
      allow create: if validateContactData();
      allow read: if isAdmin();
    }
    
    // Validation functions
    function validateUserData() {
      let data = request.resource.data;
      return data.keys().hasAll(['uid', 'email', 'createdAt']) &&
             data.uid == request.auth.uid &&
             data.email == request.auth.token.email &&
             data.createdAt == request.time;
    }
    
    function validateCommentData() {
      let data = request.resource.data;
      return data.keys().hasAll(['docId', 'userId', 'content', 'createdAt']) &&
             data.userId == request.auth.uid &&
             data.content is string &&
             data.content.size() > 0 &&
             data.content.size() <= 2000 &&
             data.createdAt == request.time;
    }
    
    function validateBookmarkData() {
      let data = request.resource.data;
      return data.keys().hasAll(['userId', 'docId', 'createdAt']) &&
             data.userId == request.auth.uid &&
             data.docId is string &&
             data.createdAt == request.time;
    }
    
    function validateFeedbackData() {
      let data = request.resource.data;
      return data.keys().hasAll(['docId', 'userId', 'rating', 'createdAt']) &&
             data.userId == request.auth.uid &&
             data.rating is number &&
             data.rating >= 1 &&
             data.rating <= 5 &&
             data.createdAt == request.time;
    }
    
    function validateAnalyticsData() {
      let data = request.resource.data;
      return data.keys().hasAll(['timestamp']) &&
             data.timestamp == request.time;
    }
    
    function validateContactData() {
      let data = request.resource.data;
      return data.keys().hasAll(['name', 'email', 'message', 'timestamp']) &&
             data.name is string &&
             data.name.size() > 0 &&
             data.email is string &&
             data.email.matches('.*@.*\\..*') &&
             data.message is string &&
             data.message.size() > 0 &&
             data.message.size() <= 5000 &&
             data.timestamp == request.time;
    }
    
    function onlyUpdating(fields) {
      return request.resource.data.diff(resource.data).affectedKeys()
             .hasOnly(fields);
    }
    
    // Rate limiting helper (basic implementation)
    function isRateLimited() {
      // This is a basic rate limiting check
      // In production, you might want more sophisticated rate limiting
      return false;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
